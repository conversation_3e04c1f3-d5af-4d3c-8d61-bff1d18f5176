# Piano WebSocket Client (C++)

C++でのPiano WebSocketクライアント実装例です。

## 依存関係

### 必要なライブラリ

1. **libwebsockets** - WebSocket通信
2. **jsoncpp** - JSON処理
3. **pthread** - マルチスレッド処理

### Ubuntu/Debianでのインストール

```bash
sudo apt update
sudo apt install libwebsockets-dev libjsoncpp-dev cmake build-essential
```

### CentOS/RHEL/Fedoraでのインストール

```bash
# Fedora
sudo dnf install libwebsockets-devel jsoncpp-devel cmake gcc-c++

# CentOS/RHEL (EPEL必要)
sudo yum install epel-release
sudo yum install libwebsockets-devel jsoncpp-devel cmake gcc-c++
```

### macOSでのインストール

```bash
brew install libwebsockets jsoncpp cmake
```

## ビルド方法

```bash
mkdir build
cd build
cmake ..
make
```

## 実行方法

```bash
# サーバーが localhost:9191 で動作していることを確認
./piano_client
```

## 使用方法

### 基本コマンド

```
play <note> <velocity> <color>  - MIDIノート再生
stop <note>                     - MIDIノート停止
name <username>                 - ユーザー名変更
users                           - アクティブユーザー一覧
demo                            - デモ演奏
quit                            - 終了
```

### 使用例

```bash
# C4ノートを赤色で再生 (velocity 80)
play 60 80 FF0000

# C4ノートを停止
stop 60

# ユーザー名を変更
name MyPianoPlayer

# アクティブユーザー一覧を表示
users

# デモ演奏（C major scale）
demo

# 終了
quit
```

## API使用例

### 基本的な使用方法

```cpp
#include "PianoClient.h"

int main() {
    PianoClient client;
    
    // コールバック設定
    client.setOnConnectedCallback([](const std::string& clientId, const User& user, const std::vector<User>& activeUsers) {
        std::cout << "Connected as: " << user.username << std::endl;
    });
    
    client.setOnMidiEventCallback([](const MidiEvent& event) {
        std::cout << "MIDI event from: " << event.fromUser.username << std::endl;
    });
    
    // 接続
    if (client.connect("localhost", 9191)) {
        // MIDI送信
        client.sendNoteOn(60, 80, 0xFF0000);  // C4, velocity 80, red
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        client.sendNoteOff(60, 0, 0xFF0000);
        
        // ユーザー名変更
        client.updateUsername("MyNewName");
    }
    
    return 0;
}
```

### コールバック関数

```cpp
// 接続完了時
client.setOnConnectedCallback([](const std::string& clientId, const User& user, const std::vector<User>& activeUsers) {
    // 接続完了処理
});

// MIDIイベント受信時
client.setOnMidiEventCallback([](const MidiEvent& event) {
    // MIDIイベント処理
    // event.statusByte: 144=NoteOn, 128=NoteOff
    // event.notenum: ノート番号 (0-127)
    // event.velocity: ベロシティ (0-127)
    // event.colorR/G/B: RGB色情報
    // event.fromUser: 送信者情報
});

// ユーザー一覧更新時
client.setOnUserUpdateCallback([](const std::vector<User>& users) {
    // ユーザー一覧更新処理
});

// エラー発生時
client.setOnErrorCallback([](const std::string& error) {
    // エラー処理
});
```

## 特徴

- **非同期処理**: 別スレッドでWebSocketイベントループを実行
- **スレッドセーフ**: マルチスレッド環境での安全な操作
- **コールバック方式**: イベント駆動型のプログラミング
- **自動再接続**: 接続エラー時の自動復旧（実装可能）
- **メモリ管理**: RAIIパターンによる安全なリソース管理

## 注意事項

- libwebsocketsのバージョンによってAPIが異なる場合があります
- 本実装はlibwebsockets 4.x系を想定しています
- プロダクション環境では適切なエラーハンドリングと再接続機能を追加してください
