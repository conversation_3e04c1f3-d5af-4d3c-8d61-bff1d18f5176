#include "PianoClient.h"
#include <iostream>
#include <thread>
#include <chrono>

class SimplePianoApp {
public:
    SimplePianoApp() {
        setupCallbacks();
    }
    
    void run() {
        std::cout << "Piano WebSocket Client Example" << std::endl;
        std::cout << "==============================" << std::endl;
        
        // サーバーに接続
        if (!client_.connect("localhost", 9191)) {
            std::cout << "Failed to connect to server" << std::endl;
            return;
        }
        
        // 接続完了まで待機
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        if (!client_.isConnected()) {
            std::cout << "Connection timeout" << std::endl;
            return;
        }
        
        // メインループ
        std::cout << "\nCommands:" << std::endl;
        std::cout << "  play <note> <velocity> <color>  - Play MIDI note (note: 0-127, velocity: 0-127, color: hex)" << std::endl;
        std::cout << "  stop <note>                     - Stop MIDI note" << std::endl;
        std::cout << "  name <username>                 - Change username" << std::endl;
        std::cout << "  users                           - List active users" << std::endl;
        std::cout << "  demo                            - Play demo sequence" << std::endl;
        std::cout << "  quit                            - Exit" << std::endl;
        std::cout << "\nEnter command: ";
        
        std::string command;
        while (std::getline(std::cin, command) && command != "quit") {
            processCommand(command);
            std::cout << "\nEnter command: ";
        }
        
        client_.disconnect();
        std::cout << "Disconnected from server" << std::endl;
    }

private:
    void setupCallbacks() {
        client_.setOnConnectedCallback([this](const std::string& clientId, const User& user, const std::vector<User>& activeUsers) {
            std::cout << "\n✓ Connected to server!" << std::endl;
            std::cout << "  Client ID: " << clientId << std::endl;
            std::cout << "  User: " << user.username << " (" << user.id << ")" << std::endl;
            std::cout << "  Active users: " << activeUsers.size() << std::endl;
        });
        
        client_.setOnMidiEventCallback([](const MidiEvent& event) {
            std::string noteType = (event.statusByte == 144) ? "ON" : "OFF";
            std::cout << "\n♪ MIDI " << noteType << " from " << event.fromUser.username
                      << ": Note=" << static_cast<int>(event.notenum)
                      << ", Velocity=" << static_cast<int>(event.velocity)
                      << ", Color=RGB(" << static_cast<int>(event.colorR)
                      << "," << static_cast<int>(event.colorG)
                      << "," << static_cast<int>(event.colorB) << ")"
                      << ", DeltaTime=" << event.deltaTime << "ms" << std::endl;
        });
        
        client_.setOnUserUpdateCallback([](const std::vector<User>& users) {
            std::cout << "\n👥 User list updated (" << users.size() << " users):" << std::endl;
            for (const auto& user : users) {
                std::cout << "  - " << user.username << " (" << user.connectedClients << " connections)" << std::endl;
            }
        });
        
        client_.setOnErrorCallback([](const std::string& error) {
            std::cout << "\n❌ Error: " << error << std::endl;
        });
    }
    
    void processCommand(const std::string& command) {
        std::istringstream iss(command);
        std::string cmd;
        iss >> cmd;
        
        if (cmd == "play") {
            int note, velocity;
            std::string colorStr;
            if (iss >> note >> velocity >> colorStr) {
                uint32_t color = parseColor(colorStr);
                client_.sendNoteOn(static_cast<uint8_t>(note), static_cast<uint8_t>(velocity), color);
                std::cout << "Sent note ON: " << note << " (velocity: " << velocity << ")" << std::endl;
            } else {
                std::cout << "Usage: play <note> <velocity> <color>" << std::endl;
            }
            
        } else if (cmd == "stop") {
            int note;
            if (iss >> note) {
                client_.sendNoteOff(static_cast<uint8_t>(note), 0, 0x000000);
                std::cout << "Sent note OFF: " << note << std::endl;
            } else {
                std::cout << "Usage: stop <note>" << std::endl;
            }
            
        } else if (cmd == "name") {
            std::string username;
            if (iss >> username) {
                client_.updateUsername(username);
                std::cout << "Username change requested: " << username << std::endl;
            } else {
                std::cout << "Usage: name <username>" << std::endl;
            }
            
        } else if (cmd == "users") {
            client_.requestUserList();
            
        } else if (cmd == "demo") {
            playDemo();
            
        } else {
            std::cout << "Unknown command: " << cmd << std::endl;
        }
    }
    
    uint32_t parseColor(const std::string& colorStr) {
        if (colorStr.length() >= 6 && colorStr[0] == '#') {
            return std::stoul(colorStr.substr(1), nullptr, 16);
        } else if (colorStr.length() == 6) {
            return std::stoul(colorStr, nullptr, 16);
        }
        return 0xFF0000; // デフォルト赤
    }
    
    void playDemo() {
        std::cout << "Playing demo sequence..." << std::endl;
        
        // C major scale
        std::vector<int> notes = {60, 62, 64, 65, 67, 69, 71, 72}; // C4 to C5
        std::vector<uint32_t> colors = {
            0xFF0000, 0xFF8000, 0xFFFF00, 0x80FF00,
            0x00FF00, 0x00FF80, 0x0080FF, 0x0000FF
        };
        
        for (size_t i = 0; i < notes.size(); ++i) {
            client_.sendNoteOn(notes[i], 80, colors[i]);
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
            client_.sendNoteOff(notes[i], 0, colors[i]);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        std::cout << "Demo completed!" << std::endl;
    }
    
    PianoClient client_;
};

int main() {
    try {
        SimplePianoApp app;
        app.run();
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
