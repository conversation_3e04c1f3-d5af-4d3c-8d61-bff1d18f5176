#include "PianoClient.h"
#include <iostream>
#include <chrono>
#include <sstream>

PianoClient::PianoClient() 
    : context_(nullptr)
    , websocket_(nullptr)
    , connected_(false)
    , shouldStop_(false)
    , port_(9191) {
    
    // プロトコル設定
    protocols_[0] = {
        "piano-protocol",
        PianoClient::websocketCallback,
        0,
        1024,
        0,
        this,
        0
    };
    protocols_[1] = { nullptr, nullptr, 0, 0, 0, nullptr, 0 };
}

PianoClient::~PianoClient() {
    disconnect();
}

bool PianoClient::connect(const std::string& host, int port) {
    if (connected_) {
        return true;
    }
    
    host_ = host;
    port_ = port;
    
    // libwebsockets コンテキスト作成
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    info.port = CONTEXT_PORT_NO_LISTEN;
    info.protocols = protocols_;
    info.gid = -1;
    info.uid = -1;
    info.options = LWS_SERVER_OPTION_DO_SSL_GLOBAL_INIT;
    
    context_ = lws_create_context(&info);
    if (!context_) {
        if (onError_) onError_("Failed to create WebSocket context");
        return false;
    }
    
    // 接続情報設定
    struct lws_client_connect_info ccinfo;
    memset(&ccinfo, 0, sizeof(ccinfo));
    ccinfo.context = context_;
    ccinfo.address = host_.c_str();
    ccinfo.port = port_;
    ccinfo.path = "/";
    ccinfo.host = ccinfo.address;
    ccinfo.origin = ccinfo.address;
    ccinfo.protocol = protocols_[0].name;
    ccinfo.userdata = this;
    
    // WebSocket接続
    websocket_ = lws_client_connect_via_info(&ccinfo);
    if (!websocket_) {
        lws_context_destroy(context_);
        context_ = nullptr;
        if (onError_) onError_("Failed to connect to WebSocket server");
        return false;
    }
    
    // イベントループスレッド開始
    shouldStop_ = false;
    eventThread_ = std::thread([this]() {
        while (!shouldStop_) {
            lws_service(context_, 50);
        }
    });
    
    return true;
}

void PianoClient::disconnect() {
    if (!connected_ && !context_) {
        return;
    }
    
    shouldStop_ = true;
    connected_ = false;
    
    if (websocket_) {
        lws_close_reason(websocket_, LWS_CLOSE_STATUS_NORMAL, nullptr, 0);
        websocket_ = nullptr;
    }
    
    if (eventThread_.joinable()) {
        eventThread_.join();
    }
    
    if (context_) {
        lws_context_destroy(context_);
        context_ = nullptr;
    }
    
    // 状態リセット
    clientId_.clear();
    currentUser_ = {};
    activeUsers_.clear();
    
    std::lock_guard<std::mutex> lock(messagesMutex_);
    while (!outgoingMessages_.empty()) {
        outgoingMessages_.pop();
    }
}

bool PianoClient::isConnected() const {
    return connected_;
}

void PianoClient::sendNoteOn(uint8_t notenum, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    auto buffer = createMidiBuffer(144, notenum, velocity, color, deltaTime); // 144 = note on
    sendMidiBuffer(buffer);
}

void PianoClient::sendNoteOff(uint8_t notenum, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    auto buffer = createMidiBuffer(128, notenum, velocity, color, deltaTime); // 128 = note off
    sendMidiBuffer(buffer);
}

void PianoClient::updateUsername(const std::string& newUsername) {
    Json::Value message;
    message["type"] = "update_username";
    message["username"] = newUsername;
    sendMessage(message);
}

void PianoClient::requestUserList() {
    Json::Value message;
    message["type"] = "get_users";
    sendMessage(message);
}

std::vector<uint8_t> PianoClient::createMidiBuffer(uint8_t statusByte, uint8_t notenum,
                                                  uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    return {
        statusByte,
        notenum,
        velocity,
        static_cast<uint8_t>((color >> 16) & 0xFF), // R
        static_cast<uint8_t>((color >> 8) & 0xFF),  // G
        static_cast<uint8_t>(color & 0xFF),         // B
        static_cast<uint8_t>((deltaTime >> 8) & 0xFF), // デルタタイム上位
        static_cast<uint8_t>(deltaTime & 0xFF)          // デルタタイム下位
    };
}

uint32_t PianoClient::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()).count();
    return static_cast<uint32_t>(timestamp);
}

void PianoClient::sendMidiBuffer(const std::vector<uint8_t>& buffer) {
    Json::Value message;
    message["type"] = "midi";

    Json::Value bufferArray(Json::arrayValue);
    for (uint8_t byte : buffer) {
        bufferArray.append(static_cast<int>(byte));
    }

    Json::Value buffersArray(Json::arrayValue);
    buffersArray.append(bufferArray);
    message["buffers"] = buffersArray; // 配列の配列として送信

    sendMessage(message);
}

void PianoClient::sendMessage(const Json::Value& message) {
    if (!connected_) return;
    
    Json::StreamWriterBuilder builder;
    std::string messageStr = Json::writeString(builder, message);
    
    std::lock_guard<std::mutex> lock(messagesMutex_);
    outgoingMessages_.push(messageStr);
    
    if (websocket_) {
        lws_callback_on_writable(websocket_);
    }
}

// コールバック設定メソッド
void PianoClient::setOnConnectedCallback(OnConnectedCallback callback) {
    std::lock_guard<std::mutex> lock(callbacksMutex_);
    onConnected_ = callback;
}

void PianoClient::setOnMidiEventCallback(OnMidiEventCallback callback) {
    std::lock_guard<std::mutex> lock(callbacksMutex_);
    onMidiEvent_ = callback;
}

void PianoClient::setOnUserUpdateCallback(OnUserUpdateCallback callback) {
    std::lock_guard<std::mutex> lock(callbacksMutex_);
    onUserUpdate_ = callback;
}

void PianoClient::setOnErrorCallback(OnErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callbacksMutex_);
    onError_ = callback;
}

// libwebsockets コールバック実装
int PianoClient::websocketCallback(struct lws *wsi, enum lws_callback_reasons reason,
                                  void *user, void *in, size_t len) {
    PianoClient* client = static_cast<PianoClient*>(lws_get_protocol(wsi)->user);
    if (!client) return 0;

    switch (reason) {
        case LWS_CALLBACK_CLIENT_ESTABLISHED:
            client->connected_ = true;
            std::cout << "WebSocket connection established" << std::endl;
            break;

        case LWS_CALLBACK_CLIENT_RECEIVE:
            {
                std::string message(static_cast<char*>(in), len);
                client->handleMessage(message);
            }
            break;

        case LWS_CALLBACK_CLIENT_WRITEABLE:
            {
                std::lock_guard<std::mutex> lock(client->messagesMutex_);
                if (!client->outgoingMessages_.empty()) {
                    std::string message = client->outgoingMessages_.front();
                    client->outgoingMessages_.pop();

                    size_t msgLen = message.length();
                    unsigned char* buf = new unsigned char[LWS_PRE + msgLen];
                    memcpy(&buf[LWS_PRE], message.c_str(), msgLen);

                    lws_write(wsi, &buf[LWS_PRE], msgLen, LWS_WRITE_TEXT);
                    delete[] buf;

                    if (!client->outgoingMessages_.empty()) {
                        lws_callback_on_writable(wsi);
                    }
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CONNECTION_ERROR:
            client->connected_ = false;
            if (client->onError_) {
                std::string error = in ? std::string(static_cast<char*>(in), len) : "Connection error";
                client->onError_(error);
            }
            break;

        case LWS_CALLBACK_CLOSED:
            client->connected_ = false;
            std::cout << "WebSocket connection closed" << std::endl;
            break;

        default:
            break;
    }

    return 0;
}

void PianoClient::handleMessage(const std::string& message) {
    try {
        Json::CharReaderBuilder builder;
        Json::Value root;
        std::string errors;

        std::istringstream stream(message);
        if (!Json::parseFromStream(builder, stream, &root, &errors)) {
            if (onError_) onError_("Failed to parse JSON: " + errors);
            return;
        }

        std::string type = root["type"].asString();

        if (type == "connected") {
            clientId_ = root["clientId"].asString();

            // ユーザー情報設定
            Json::Value userJson = root["user"];
            currentUser_.id = userJson["id"].asString();
            currentUser_.username = userJson["username"].asString();

            // アクティブユーザー一覧
            std::vector<User> users;
            Json::Value activeUsersJson = root["activeUsers"];
            for (const auto& userJson : activeUsersJson) {
                User user;
                user.id = userJson["id"].asString();
                user.username = userJson["username"].asString();
                user.connectedClients = userJson["connectedClients"].asInt();
                users.push_back(user);
            }

            {
                std::lock_guard<std::mutex> lock(usersMutex_);
                activeUsers_ = users;
            }

            if (onConnected_) {
                onConnected_(clientId_, currentUser_, users);
            }

        } else if (type == "username_updated") {
            currentUser_.username = root["username"].asString();

        } else if (type == "midi") {
            // 複数バッファ対応（新旧両対応）
            Json::Value buffers;
            if (root.isMember("buffers")) {
                buffers = root["buffers"];
            } else if (root.isMember("buffer")) {
                buffers = Json::Value(Json::arrayValue);
                buffers.append(root["buffer"]);
            }

            // 各バッファを処理
            for (const auto& buffer : buffers) {
                MidiEvent event;

                // バッファデータ（8バイト形式）
                if (buffer.size() >= 8) {
                    event.statusByte = buffer[0].asUInt();
                    event.notenum = buffer[1].asUInt();
                    event.velocity = buffer[2].asUInt();
                    event.colorR = buffer[3].asUInt();
                    event.colorG = buffer[4].asUInt();
                    event.colorB = buffer[5].asUInt();
                    event.deltaTimeHigh = buffer[6].asUInt();
                    event.deltaTimeLow = buffer[7].asUInt();
                    event.deltaTime = (buffer[6].asUInt() << 8) | buffer[7].asUInt();
                }

                // 送信者情報
                if (root.isMember("fromUser")) {
                    Json::Value fromUser = root["fromUser"];
                    event.fromUser.id = fromUser["id"].asString();
                    event.fromUser.username = fromUser["username"].asString();
                }

                event.timestamp = root["timestamp"].asUInt64();

                if (onMidiEvent_) {
                    onMidiEvent_(event);
                }
            }

        } else if (type == "user_update" || type == "user_list") {
            std::vector<User> users;
            Json::Value usersJson = root["users"];
            for (const auto& userJson : usersJson) {
                User user;
                user.id = userJson["id"].asString();
                user.username = userJson["username"].asString();
                user.connectedClients = userJson["connectedClients"].asInt();
                users.push_back(user);
            }

            {
                std::lock_guard<std::mutex> lock(usersMutex_);
                activeUsers_ = users;
            }

            if (onUserUpdate_) {
                onUserUpdate_(users);
            }

        } else if (type == "error") {
            if (onError_) {
                onError_(root["message"].asString());
            }
        }

    } catch (const std::exception& e) {
        if (onError_) {
            onError_("Exception in handleMessage: " + std::string(e.what()));
        }
    }
}
