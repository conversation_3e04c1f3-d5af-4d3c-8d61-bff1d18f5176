#!/bin/bash

# Piano WebSocket Client Build Script

echo "Building Piano WebSocket Client..."

# 依存関係チェック
check_dependency() {
    if ! pkg-config --exists $1; then
        echo "Error: $1 is not installed"
        echo "Please install required dependencies:"
        echo "  Ubuntu/Debian: sudo apt install libwebsockets-dev libjsoncpp-dev"
        echo "  Fedora: sudo dnf install libwebsockets-devel jsoncpp-devel"
        echo "  macOS: brew install libwebsockets jsoncpp"
        exit 1
    fi
}

echo "Checking dependencies..."
check_dependency "libwebsockets"
check_dependency "jsoncpp"

# ビルドディレクトリ作成
if [ ! -d "build" ]; then
    mkdir build
fi

cd build

# CMake設定
echo "Configuring with CMake..."
cmake .. || {
    echo "CMake configuration failed"
    exit 1
}

# ビルド実行
echo "Building..."
make -j$(nproc) || {
    echo "Build failed"
    exit 1
}

echo "Build completed successfully!"
echo "Run with: ./build/piano_client"
