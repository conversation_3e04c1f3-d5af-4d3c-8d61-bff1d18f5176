#pragma once

#include <libwebsockets.h>
#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <json/json.h>

struct User {
    std::string id;
    std::string username;
    int connectedClients;
};

struct MidiEvent {
    uint8_t statusByte;
    uint8_t notenum;
    uint8_t velocity;
    uint8_t colorR;
    uint8_t colorG;
    uint8_t colorB;
    uint8_t deltaTimeHigh;
    uint8_t deltaTimeLow;
    uint16_t deltaTime;

    User fromUser;
    uint64_t timestamp;
};

class PianoClient {
public:
    // コールバック関数の型定義
    using OnConnectedCallback = std::function<void(const std::string& clientId, const User& user, const std::vector<User>& activeUsers)>;
    using OnMidiEventCallback = std::function<void(const MidiEvent& event)>;
    using OnUserUpdateCallback = std::function<void(const std::vector<User>& users)>;
    using OnErrorCallback = std::function<void(const std::string& error)>;

    PianoClient();
    ~PianoClient();

    // 接続・切断
    bool connect(const std::string& host = "localhost", int port = 9191);
    void disconnect();
    bool isConnected() const;

    // MIDI送信
    void sendNoteOn(uint8_t notenum, uint8_t velocity, uint32_t color, uint16_t deltaTime = 0);
    void sendNoteOff(uint8_t notenum, uint8_t velocity, uint32_t color, uint16_t deltaTime = 0);
    
    // ユーザー管理
    void updateUsername(const std::string& newUsername);
    void requestUserList();
    
    // コールバック設定
    void setOnConnectedCallback(OnConnectedCallback callback);
    void setOnMidiEventCallback(OnMidiEventCallback callback);
    void setOnUserUpdateCallback(OnUserUpdateCallback callback);
    void setOnErrorCallback(OnErrorCallback callback);
    
    // ユーザー情報取得
    const User& getCurrentUser() const { return currentUser_; }
    const std::vector<User>& getActiveUsers() const { return activeUsers_; }

private:
    // libwebsockets コールバック
    static int websocketCallback(struct lws *wsi, enum lws_callback_reasons reason,
                                void *user, void *in, size_t len);
    
    // メッセージ処理
    void handleMessage(const std::string& message);
    void sendMessage(const Json::Value& message);
    void sendMidiBuffer(const std::vector<uint8_t>& buffer);
    
    // ヘルパー関数
    std::vector<uint8_t> createMidiBuffer(uint8_t statusByte, uint8_t notenum,
                                         uint8_t velocity, uint32_t color, uint16_t deltaTime = 0);
    uint32_t getCurrentTimestamp();
    
    // WebSocket関連
    struct lws_context* context_;
    struct lws* websocket_;
    struct lws_protocols protocols_[2];
    
    // 接続状態
    std::atomic<bool> connected_;
    std::atomic<bool> shouldStop_;
    std::thread eventThread_;
    
    // ユーザー情報
    std::string clientId_;
    User currentUser_;
    std::vector<User> activeUsers_;
    std::mutex usersMutex_;
    
    // メッセージキュー
    std::queue<std::string> outgoingMessages_;
    std::mutex messagesMutex_;
    
    // コールバック
    OnConnectedCallback onConnected_;
    OnMidiEventCallback onMidiEvent_;
    OnUserUpdateCallback onUserUpdate_;
    OnErrorCallback onError_;
    std::mutex callbacksMutex_;
    
    // 設定
    std::string host_;
    int port_;
};
