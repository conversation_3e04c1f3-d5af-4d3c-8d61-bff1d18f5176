cmake_minimum_required(VERSION 3.10)
project(PianoWebSocketClient)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 依存関係を見つける
find_package(PkgConfig REQUIRED)

# libwebsockets
pkg_check_modules(LIBWEBSOCKETS REQUIRED libwebsockets)

# jsoncpp
find_package(jsoncpp REQUIRED)

# ソースファイル
set(SOURCES
    main.cpp
    PianoClient.cpp
)

# 実行ファイル作成
add_executable(piano_client ${SOURCES})

# インクルードディレクトリ
target_include_directories(piano_client PRIVATE
    ${LIBWEBSOCKETS_INCLUDE_DIRS}
)

# ライブラリリンク
target_link_libraries(piano_client
    ${LIBWEBSOCKETS_LIBRARIES}
    jsoncpp_lib
    pthread
)

# コンパイラフラグ
target_compile_options(piano_client PRIVATE
    ${LIBWEBSOCKETS_CFLAGS_OTHER}
)

# リンカフラグ
target_link_directories(piano_client PRIVATE
    ${LIBWEBSOCKETS_LIBRARY_DIRS}
)
