# Piano WebSocket Server

MIDIイベント（note on/off, notenum, velocity, color, time）をリアルタイムでブロードキャストするWebSocketサーバーです。

## 機能

- **ユーザー管理**: ユーザー認証、セッション管理、アクティブユーザー一覧
- **MIDIイベントブロードキャスト**: クライアントから送信されたMIDIイベントを他の接続中のクライアントにリアルタイムで配信
- **メモリリーク対策**: 適切なリソース管理、デッドコネクション検出、自動クリーンアップ
- **Pingイベント**: 30秒ごとに自動でpingを送信してコネクションの健全性を確認
- **クライアント管理**: 各クライアントにユニークIDを割り当てて管理
- **カラー情報**: 各MIDIイベントにHEXカラー情報を付加

## MIDIイベントフォーマット

### 送信フォーマット（クライアント→サーバー）
```json
{
  "type": "midi",
  "buffer": [144, 60, 64, 255, 0, 0, 123, 45]
}
```

バッファの構造（8バイト）:
- `buffer[0]`: ステータスバイト (144=note on, 128=note off)
- `buffer[1]`: ノート番号 (0-127)
- `buffer[2]`: ベロシティ (0-127)
- `buffer[3]`: カラーR値 (0-255)
- `buffer[4]`: カラーG値 (0-255)
- `buffer[5]`: カラーB値 (0-255)
- `buffer[6]`: 時間上位バイト
- `buffer[7]`: 時間下位バイト

### 受信フォーマット（サーバー→クライアント）
```json
{
  "type": "midi",
  "buffer": [144, 60, 64, 255, 0, 0, 123, 45]
}
```

バッファの構造は送信時と同じ8バイト形式です。

## セットアップ

1. 依存関係のインストール:
```bash
npm install
```

2. サーバー起動:
```bash
node index.js
```

サーバーはポート9191で起動します。

## クライアントの使用方法

### HTMLクライアント
`client-example.html`をブラウザで開いて使用できます。

### カスタムクライアント実装

#### 接続
```javascript
const ws = new WebSocket('ws://localhost:9191');

ws.onopen = () => {
  console.log('接続完了');
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);

  if (data.type === 'connected') {
    console.log('ユーザー情報:', data.user);
    console.log('アクティブユーザー:', data.activeUsers);

    // ユーザー名を変更したい場合
    ws.send(JSON.stringify({
      type: 'update_username',
      username: '新しいユーザー名'
    }));
  }
};
```

#### MIDIイベント送信
```javascript
// Note On イベント
const noteOnBuffer = [144, 60, 64, 255, 0, 0, 0, 0]; // C4, velocity 64, red color
ws.send(JSON.stringify({
  type: 'midi',
  buffer: noteOnBuffer
}));

// Note Off イベント
const noteOffBuffer = [128, 60, 0, 0, 0, 0, 0, 0]; // C4 off
ws.send(JSON.stringify({
  type: 'midi',
  buffer: noteOffBuffer
}));
```

#### イベント受信
```javascript
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);

  switch (data.type) {
    case 'connected':
      console.log('接続完了:', data.clientId);
      break;

    case 'midi':
      // バッファからMIDIイベントを解析
      const buffer = data.buffer;
      for (let i = 0; i < buffer.length; i += 8) {
        const midiEvent = {
          type: buffer[i] === 144 ? 'noteon' : 'noteoff',
          notenum: buffer[i + 1],
          velocity: buffer[i + 2],
          color: `#${buffer[i + 3].toString(16).padStart(2, '0')}${buffer[i + 4].toString(16).padStart(2, '0')}${buffer[i + 5].toString(16).padStart(2, '0')}`,
          time: (buffer[i + 6] << 8) | buffer[i + 7]
        };
        console.log('MIDI受信:', midiEvent);
        // ここでMIDIイベントを処理
      }
      break;

    case 'pong':
      console.log('Pong受信');
      break;
  }
};
```

## ユーザー管理

### 自動ユーザー作成
1. WebSocket接続
2. サーバーが自動的にランダムなユーザーID・ユーザー名を生成
3. `connected`メッセージでユーザー情報とアクティブユーザー一覧を受信
4. 接続後にユーザー名の変更が可能

### ユーザー情報
- **ユーザーID**: 自動生成される一意識別子
- **ユーザー名**: 自動生成（例: ユーザー123）、後から変更可能
- **セッション管理**: 同一ユーザーの複数接続対応
- **アクティブ状態**: オンライン/オフライン管理

## メッセージタイプ

### クライアント→サーバー
- `update_username`: ユーザー名変更
- `midi`: MIDIイベントデータ
- `ping`: Pingリクエスト
- `get_users`: アクティブユーザー一覧取得

### サーバー→クライアント
- `connected`: 接続確認（ユーザー情報・アクティブユーザー一覧付き）
- `username_updated`: ユーザー名更新成功
- `midi`: MIDIイベントブロードキャスト（送信者情報付き）
- `user_update`: ユーザー一覧更新
- `user_list`: ユーザー一覧レスポンス
- `pong`: Pingレスポンス
- `error`: エラーメッセージ

## メモリリーク対策

### 自動クリーンアップ
- **デッドコネクション検出**: 60秒以上Pingがないクライアントを自動切断
- **非アクティブユーザー削除**: 24時間非アクティブなユーザー情報を削除
- **リソース管理**: タイマー、イベントリスナーの適切な削除
- **グレースフルシャットダウン**: SIGINT/SIGTERMでの適切な終了処理

## 開発・デバッグ

サーバーはコンソールに詳細なログを出力します：
- 新しい接続
- MIDIイベントの受信と配信
- Ping/Pongイベント
- エラー情報

## 注意事項

- サーバーは送信者以外の全クライアントにMIDIイベントをブロードキャストします
- Pingは30秒間隔で自動送信されます
- 不正なJSONメッセージは従来通りの文字列ブロードキャストとして処理されます
