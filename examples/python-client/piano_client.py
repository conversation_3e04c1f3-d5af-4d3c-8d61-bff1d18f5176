#!/usr/bin/env python3
"""
Piano WebSocket Client - Python Implementation
"""

import websocket
import json
import time
import threading
from typing import Callable, Optional, List, Dict, Any

class User:
    def __init__(self, id: str, username: str, connected_clients: int = 0):
        self.id = id
        self.username = username
        self.connected_clients = connected_clients

class MidiEvent:
    def __init__(self, buffer: List[int], from_user: Optional[User] = None, timestamp: int = 0):
        self.status_byte = buffer[0] if len(buffer) > 0 else 0
        self.notenum = buffer[1] if len(buffer) > 1 else 0
        self.velocity = buffer[2] if len(buffer) > 2 else 0
        self.color_r = buffer[3] if len(buffer) > 3 else 0
        self.color_g = buffer[4] if len(buffer) > 4 else 0
        self.color_b = buffer[5] if len(buffer) > 5 else 0
        self.delta_time_high = buffer[6] if len(buffer) > 6 else 0
        self.delta_time_low = buffer[7] if len(buffer) > 7 else 0
        self.delta_time = (self.delta_time_high << 8) | self.delta_time_low

        self.from_user = from_user
        self.timestamp = timestamp

class PianoClient:
    def __init__(self):
        self.ws: Optional[websocket.WebSocketApp] = None
        self.client_id: Optional[str] = None
        self.user: Optional[User] = None
        self.active_users: List[User] = []
        self.connected = False
        
        # コールバック関数
        self.on_connected: Optional[Callable] = None
        self.on_midi_event: Optional[Callable] = None
        self.on_user_update: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
    def connect(self, url: str = "ws://localhost:9191") -> bool:
        """サーバーに接続"""
        try:
            self.ws = websocket.WebSocketApp(url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close)
            
            # 別スレッドで実行
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            # 接続完了まで少し待機
            time.sleep(0.5)
            return self.connected
            
        except Exception as e:
            if self.on_error:
                self.on_error(f"Connection failed: {e}")
            return False
    
    def disconnect(self):
        """接続を切断"""
        if self.ws:
            self.ws.close()
        self.connected = False
    
    def send_note_on(self, notenum: int, velocity: int, color: int, delta_time: int = 0):
        """Note Onイベントを送信"""
        buffer = self._create_midi_buffer(144, notenum, velocity, color, delta_time)
        self._send_midi_buffer(buffer)

    def send_note_off(self, notenum: int, velocity: int, color: int, delta_time: int = 0):
        """Note Offイベントを送信"""
        buffer = self._create_midi_buffer(128, notenum, velocity, color, delta_time)
        self._send_midi_buffer(buffer)
    
    def update_username(self, username: str):
        """ユーザー名を更新"""
        self._send_message({
            "type": "update_username",
            "username": username
        })
    
    def request_user_list(self):
        """ユーザー一覧を要求"""
        self._send_message({
            "type": "get_users"
        })
    
    def _create_midi_buffer(self, status_byte: int, notenum: int, velocity: int, color: int, delta_time: int = 0) -> List[int]:
        """MIDIバッファを作成（8バイト形式、デルタタイム付き）"""
        return [
            status_byte,
            notenum,
            velocity,
            (color >> 16) & 0xFF,  # R
            (color >> 8) & 0xFF,   # G
            color & 0xFF,          # B
            (delta_time >> 8) & 0xFF,  # デルタタイム上位
            delta_time & 0xFF          # デルタタイム下位
        ]
    
    def _send_midi_buffer(self, buffer: List[int]):
        """MIDIバッファを送信"""
        self._send_message({
            "type": "midi",
            "buffers": [buffer]  # 配列の配列として送信
        })
    
    def _send_message(self, message: Dict[str, Any]):
        """メッセージを送信"""
        if self.ws and self.connected:
            try:
                self.ws.send(json.dumps(message))
            except Exception as e:
                if self.on_error:
                    self.on_error(f"Send failed: {e}")
    
    def _on_open(self, ws):
        """接続開始時のコールバック"""
        self.connected = True
        print("WebSocket connection opened")
    
    def _on_message(self, ws, message):
        """メッセージ受信時のコールバック"""
        try:
            data = json.loads(message)
            self._handle_message(data)
        except json.JSONDecodeError as e:
            if self.on_error:
                self.on_error(f"JSON decode error: {e}")
    
    def _on_error(self, ws, error):
        """エラー発生時のコールバック"""
        if self.on_error:
            self.on_error(f"WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """接続終了時のコールバック"""
        self.connected = False
        print("WebSocket connection closed")
    
    def _handle_message(self, data: Dict[str, Any]):
        """受信メッセージを処理"""
        msg_type = data.get("type")
        
        if msg_type == "connected":
            self.client_id = data.get("clientId")
            
            # ユーザー情報設定
            user_data = data.get("user", {})
            self.user = User(
                id=user_data.get("id", ""),
                username=user_data.get("username", "")
            )
            
            # アクティブユーザー一覧
            active_users_data = data.get("activeUsers", [])
            self.active_users = [
                User(
                    id=user.get("id", ""),
                    username=user.get("username", ""),
                    connected_clients=user.get("connectedClients", 0)
                )
                for user in active_users_data
            ]
            
            if self.on_connected:
                self.on_connected(self.client_id, self.user, self.active_users)
                
        elif msg_type == "username_updated":
            if self.user:
                self.user.username = data.get("username", "")
                
        elif msg_type == "midi":
            # 複数バッファ対応（新旧両対応）
            buffers = data.get("buffers", [data.get("buffer", [])])
            from_user_data = data.get("fromUser")
            from_user = None

            if from_user_data:
                from_user = User(
                    id=from_user_data.get("id", ""),
                    username=from_user_data.get("username", "")
                )

            # 各バッファを処理
            for buffer in buffers:
                midi_event = MidiEvent(
                    buffer=buffer,
                    from_user=from_user,
                    timestamp=data.get("timestamp", 0)
                )

                if self.on_midi_event:
                    self.on_midi_event(midi_event)
                
        elif msg_type in ["user_update", "user_list"]:
            users_data = data.get("users", [])
            self.active_users = [
                User(
                    id=user.get("id", ""),
                    username=user.get("username", ""),
                    connected_clients=user.get("connectedClients", 0)
                )
                for user in users_data
            ]
            
            if self.on_user_update:
                self.on_user_update(self.active_users)
                
        elif msg_type == "error":
            if self.on_error:
                self.on_error(data.get("message", "Unknown error"))

# 使用例
if __name__ == "__main__":
    def on_connected(client_id, user, active_users):
        print(f"Connected as: {user.username} ({user.id})")
        print(f"Active users: {len(active_users)}")
    
    def on_midi_event(event):
        note_type = "ON" if event.status_byte == 144 else "OFF"
        from_user = event.from_user.username if event.from_user else "Unknown"
        print(f"MIDI {note_type} from {from_user}: Note={event.notenum}, Velocity={event.velocity}")
    
    def on_user_update(users):
        print(f"User list updated: {len(users)} users")
        for user in users:
            print(f"  - {user.username} ({user.connected_clients} connections)")
    
    def on_error(error):
        print(f"Error: {error}")
    
    # クライアント作成
    client = PianoClient()
    client.on_connected = on_connected
    client.on_midi_event = on_midi_event
    client.on_user_update = on_user_update
    client.on_error = on_error
    
    # 接続
    if client.connect():
        print("Connected to server!")
        
        # デモ演奏
        time.sleep(1)
        
        # C major scale
        notes = [60, 62, 64, 65, 67, 69, 71, 72]  # C4 to C5
        colors = [0xFF0000, 0xFF8000, 0xFFFF00, 0x80FF00, 0x00FF00, 0x00FF80, 0x0080FF, 0x0000FF]
        
        for note, color in zip(notes, colors):
            client.send_note_on(note, 80, color)
            time.sleep(0.3)
            client.send_note_off(note, 0, color)
            time.sleep(0.1)
        
        # ユーザー名変更
        client.update_username("PythonPlayer")
        
        # 10秒間待機
        time.sleep(10)
        
        client.disconnect()
    else:
        print("Failed to connect to server")
