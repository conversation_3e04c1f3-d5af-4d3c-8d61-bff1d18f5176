# Piano WebSocket Client Examples

各言語でのPiano WebSocketクライアント実装例です。

## 利用可能な実装

### 1. JavaScript (ブラウザ)
- **ファイル**: `../client-example.html`
- **特徴**: ブラウザで直接実行可能、視覚的なピアノインターフェース
- **実行方法**: ブラウザでHTMLファイルを開く

### 2. C++
- **ディレクトリ**: `../cpp-client-example/`
- **特徴**: 高性能、マルチスレッド対応、libwebsockets使用
- **実行方法**: CMakeでビルド後実行

### 3. Python
- **ディレクトリ**: `python-client/`
- **特徴**: シンプルな実装、websocket-client使用
- **実行方法**: 
  ```bash
  cd python-client
  pip install -r requirements.txt
  python piano_client.py
  ```

### 4. Node.js
- **ディレクトリ**: `nodejs-client/`
- **特徴**: 非同期処理、ws ライブラリ使用
- **実行方法**:
  ```bash
  cd nodejs-client
  npm install
  npm start
  ```

## 共通機能

すべての実装で以下の機能を提供：

### 基本機能
- **自動接続**: サーバーへの自動接続とユーザー作成
- **MIDI送信**: Note On/Off イベントの送信
- **MIDI受信**: 他のクライアントからのMIDIイベント受信
- **ユーザー管理**: ユーザー名変更、アクティブユーザー一覧

### コールバック機能
- **接続完了**: 接続時のコールバック
- **MIDIイベント**: MIDI受信時のコールバック
- **ユーザー更新**: ユーザー一覧更新時のコールバック
- **エラー処理**: エラー発生時のコールバック

## 使用例

### 基本的な使用パターン

```javascript
// 1. クライアント作成
const client = new PianoClient();

// 2. コールバック設定
client.onConnected = (clientId, user, activeUsers) => {
    console.log(`Connected as: ${user.username}`);
};

client.onMidiEvent = (event) => {
    console.log(`MIDI from ${event.fromUser.username}`);
};

// 3. 接続
await client.connect('ws://localhost:9191');

// 4. MIDI送信
client.sendNoteOn(60, 80, 0xFF0000);  // C4, velocity 80, red
client.sendNoteOff(60, 0, 0xFF0000);

// 5. ユーザー名変更
client.updateUsername('MyNewName');
```

## パフォーマンス比較

| 言語 | 接続速度 | メモリ使用量 | CPU使用率 | 実装難易度 |
|------|----------|--------------|-----------|------------|
| JavaScript (Browser) | ★★★★☆ | ★★★☆☆ | ★★★☆☆ | ★★★★★ |
| C++ | ★★★★★ | ★★★★★ | ★★★★★ | ★★☆☆☆ |
| Python | ★★★☆☆ | ★★☆☆☆ | ★★☆☆☆ | ★★★★☆ |
| Node.js | ★★★★☆ | ★★★☆☆ | ★★★★☆ | ★★★★☆ |

## 選択指針

### JavaScript (ブラウザ)
- **適用場面**: Webアプリケーション、プロトタイピング
- **メリット**: 簡単実装、視覚的UI、クロスプラットフォーム
- **デメリット**: ブラウザ依存、パフォーマンス制限

### C++
- **適用場面**: 高性能アプリケーション、リアルタイム処理
- **メリット**: 最高性能、低レイテンシ、メモリ効率
- **デメリット**: 実装複雑、プラットフォーム依存

### Python
- **適用場面**: プロトタイピング、データ処理、AI連携
- **メリット**: 簡単実装、豊富なライブラリ、可読性
- **デメリット**: 実行速度、GIL制限

### Node.js
- **適用場面**: サーバーサイド、リアルタイムアプリ
- **メリット**: 非同期処理、JavaScript統一、npm生態系
- **デメリット**: シングルスレッド、CPU集約的処理

## 拡張例

### 録音・再生機能
```javascript
class RecordingClient extends PianoClient {
    constructor() {
        super();
        this.recording = [];
        this.isRecording = false;
    }
    
    startRecording() {
        this.recording = [];
        this.isRecording = true;
    }
    
    stopRecording() {
        this.isRecording = false;
        return this.recording;
    }
    
    onMidiEvent(event) {
        if (this.isRecording) {
            this.recording.push({
                ...event,
                timestamp: Date.now()
            });
        }
        super.onMidiEvent(event);
    }
}
```

### ルーム機能
```javascript
class RoomClient extends PianoClient {
    joinRoom(roomId) {
        this.sendMessage({
            type: 'join_room',
            roomId: roomId
        });
    }
    
    leaveRoom() {
        this.sendMessage({
            type: 'leave_room'
        });
    }
}
```

## トラブルシューティング

### よくある問題

1. **接続エラー**
   - サーバーが起動しているか確認
   - ポート番号（9191）が正しいか確認
   - ファイアウォール設定を確認

2. **依存関係エラー**
   - 各言語の依存関係が正しくインストールされているか確認
   - バージョン互換性を確認

3. **MIDI送信エラー**
   - バッファサイズが8バイトか確認
   - ノート番号・ベロシティの範囲を確認（0-127）

### デバッグ方法

1. **ログ出力の有効化**
2. **ネットワーク通信の監視**
3. **サーバーログの確認**

## 貢献

新しい言語での実装や機能追加は歓迎します：

1. 新しい言語ディレクトリを作成
2. 基本機能を実装
3. README.mdとサンプルコードを追加
4. テストケースを作成
