#!/usr/bin/env node
/**
 * Piano WebSocket Client - Node.js Implementation
 */

const WebSocket = require('ws');

class User {
    constructor(id, username, connectedClients = 0) {
        this.id = id;
        this.username = username;
        this.connectedClients = connectedClients;
    }
}

class MidiEvent {
    constructor(buffer, fromUser = null, timestamp = 0) {
        this.statusByte = buffer[0] || 0;
        this.notenum = buffer[1] || 0;
        this.velocity = buffer[2] || 0;
        this.colorR = buffer[3] || 0;
        this.colorG = buffer[4] || 0;
        this.colorB = buffer[5] || 0;
        this.deltaTimeHigh = buffer[6] || 0;
        this.deltaTimeLow = buffer[7] || 0;
        this.deltaTime = (this.deltaTimeHigh << 8) | this.deltaTimeLow;

        this.fromUser = fromUser;
        this.timestamp = timestamp;
    }
}

class PianoClient {
    constructor() {
        this.ws = null;
        this.clientId = null;
        this.user = null;
        this.activeUsers = [];
        this.connected = false;
        
        // コールバック関数
        this.onConnected = null;
        this.onMidiEvent = null;
        this.onUserUpdate = null;
        this.onError = null;
    }
    
    /**
     * サーバーに接続
     * @param {string} url - WebSocket URL
     * @returns {Promise<boolean>} 接続成功可否
     */
    connect(url = 'ws://localhost:9191') {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(url);
                
                this.ws.on('open', () => {
                    this.connected = true;
                    console.log('WebSocket connection opened');
                    resolve(true);
                });
                
                this.ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        this.handleMessage(message);
                    } catch (error) {
                        if (this.onError) {
                            this.onError(`JSON parse error: ${error.message}`);
                        }
                    }
                });
                
                this.ws.on('error', (error) => {
                    this.connected = false;
                    if (this.onError) {
                        this.onError(`WebSocket error: ${error.message}`);
                    }
                    reject(error);
                });
                
                this.ws.on('close', () => {
                    this.connected = false;
                    console.log('WebSocket connection closed');
                });
                
            } catch (error) {
                if (this.onError) {
                    this.onError(`Connection failed: ${error.message}`);
                }
                reject(error);
            }
        });
    }
    
    /**
     * 接続を切断
     */
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
        this.connected = false;
    }
    
    /**
     * Note Onイベントを送信
     * @param {number} notenum - ノート番号 (0-127)
     * @param {number} velocity - ベロシティ (0-127)
     * @param {number} color - 色情報 (0xRRGGBB)
     */
    sendNoteOn(notenum, velocity, color, deltaTime = 0) {
        const buffer = this.createMidiBuffer(144, notenum, velocity, color, deltaTime);
        this.sendMidiBuffer(buffer);
    }

    /**
     * Note Offイベントを送信
     * @param {number} notenum - ノート番号 (0-127)
     * @param {number} velocity - ベロシティ (0-127)
     * @param {number} color - 色情報 (0xRRGGBB)
     * @param {number} deltaTime - デルタタイム (0-65535ms)
     */
    sendNoteOff(notenum, velocity, color, deltaTime = 0) {
        const buffer = this.createMidiBuffer(128, notenum, velocity, color, deltaTime);
        this.sendMidiBuffer(buffer);
    }
    
    /**
     * ユーザー名を更新
     * @param {string} username - 新しいユーザー名
     */
    updateUsername(username) {
        this.sendMessage({
            type: 'update_username',
            username: username
        });
    }
    
    /**
     * ユーザー一覧を要求
     */
    requestUserList() {
        this.sendMessage({
            type: 'get_users'
        });
    }
    
    /**
     * MIDIバッファを作成
     * @param {number} statusByte - ステータスバイト
     * @param {number} notenum - ノート番号
     * @param {number} velocity - ベロシティ
     * @param {number} color - 色情報
     * @returns {Array<number>} MIDIバッファ
     */
    createMidiBuffer(statusByte, notenum, velocity, color, deltaTime = 0) {
        return [
            statusByte,
            notenum,
            velocity,
            (color >> 16) & 0xFF,  // R
            (color >> 8) & 0xFF,   // G
            color & 0xFF,          // B
            (deltaTime >> 8) & 0xFF,  // デルタタイム上位
            deltaTime & 0xFF          // デルタタイム下位
        ];
    }
    
    /**
     * MIDIバッファを送信
     * @param {Array<number>} buffer - MIDIバッファ
     */
    sendMidiBuffer(buffer) {
        this.sendMessage({
            type: 'midi',
            buffers: [buffer] // 配列の配列として送信
        });
    }
    
    /**
     * メッセージを送信
     * @param {Object} message - 送信メッセージ
     */
    sendMessage(message) {
        if (this.ws && this.connected) {
            try {
                this.ws.send(JSON.stringify(message));
            } catch (error) {
                if (this.onError) {
                    this.onError(`Send failed: ${error.message}`);
                }
            }
        }
    }
    
    /**
     * 受信メッセージを処理
     * @param {Object} data - 受信データ
     */
    handleMessage(data) {
        switch (data.type) {
            case 'connected':
                this.clientId = data.clientId;
                
                // ユーザー情報設定
                const userData = data.user || {};
                this.user = new User(userData.id || '', userData.username || '');
                
                // アクティブユーザー一覧
                const activeUsersData = data.activeUsers || [];
                this.activeUsers = activeUsersData.map(user => 
                    new User(user.id || '', user.username || '', user.connectedClients || 0)
                );
                
                if (this.onConnected) {
                    this.onConnected(this.clientId, this.user, this.activeUsers);
                }
                break;
                
            case 'username_updated':
                if (this.user) {
                    this.user.username = data.username || '';
                }
                break;
                
            case 'midi':
                // 複数バッファ対応（新旧両対応）
                const buffers = data.buffers || [data.buffer || []];
                let fromUser = null;

                if (data.fromUser) {
                    fromUser = new User(
                        data.fromUser.id || '',
                        data.fromUser.username || ''
                    );
                }

                // 各バッファを処理
                buffers.forEach(buffer => {
                    const midiEvent = new MidiEvent(
                        buffer,
                        fromUser,
                        data.timestamp || 0
                    );

                    if (this.onMidiEvent) {
                        this.onMidiEvent(midiEvent);
                    }
                });
                break;
                
            case 'user_update':
            case 'user_list':
                const usersData = data.users || [];
                this.activeUsers = usersData.map(user => 
                    new User(user.id || '', user.username || '', user.connectedClients || 0)
                );
                
                if (this.onUserUpdate) {
                    this.onUserUpdate(this.activeUsers);
                }
                break;
                
            case 'error':
                if (this.onError) {
                    this.onError(data.message || 'Unknown error');
                }
                break;
                
            default:
                console.log(`Unknown message type: ${data.type}`);
        }
    }
}

// 使用例
async function main() {
    const client = new PianoClient();
    
    // コールバック設定
    client.onConnected = (clientId, user, activeUsers) => {
        console.log(`Connected as: ${user.username} (${user.id})`);
        console.log(`Active users: ${activeUsers.length}`);
    };
    
    client.onMidiEvent = (event) => {
        const noteType = event.statusByte === 144 ? 'ON' : 'OFF';
        const fromUser = event.fromUser ? event.fromUser.username : 'Unknown';
        console.log(`MIDI ${noteType} from ${fromUser}: Note=${event.notenum}, Velocity=${event.velocity}`);
    };
    
    client.onUserUpdate = (users) => {
        console.log(`User list updated: ${users.length} users`);
        users.forEach(user => {
            console.log(`  - ${user.username} (${user.connectedClients} connections)`);
        });
    };
    
    client.onError = (error) => {
        console.error(`Error: ${error}`);
    };
    
    try {
        // 接続
        await client.connect();
        console.log('Connected to server!');
        
        // 1秒待機
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // デモ演奏
        const notes = [60, 62, 64, 65, 67, 69, 71, 72];  // C4 to C5
        const colors = [0xFF0000, 0xFF8000, 0xFFFF00, 0x80FF00, 0x00FF00, 0x00FF80, 0x0080FF, 0x0000FF];
        
        for (let i = 0; i < notes.length; i++) {
            client.sendNoteOn(notes[i], 80, colors[i]);
            await new Promise(resolve => setTimeout(resolve, 300));
            client.sendNoteOff(notes[i], 0, colors[i]);
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // ユーザー名変更
        client.updateUsername('NodeJSPlayer');
        
        // 10秒間待機
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        client.disconnect();
        
    } catch (error) {
        console.error('Failed to connect to server:', error.message);
    }
}

// スクリプトとして実行された場合
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { PianoClient, User, MidiEvent };
