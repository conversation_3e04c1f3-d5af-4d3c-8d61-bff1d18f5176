const ws = require('ws');
const http = require('http');

const server = http.createServer((req, res) => {
    res.writeHead(200);
    res.end('Hello World!');
});

// WebSocketサーバーの設定（バッファリング最適化）
const wss = new ws.Server({
    server,
    // バックプレッシャー対応のための設定
    perMessageDeflate: {
        // 圧縮を有効にしてバンド幅を節約
        threshold: 1024,
        // 圧縮レベルを調整（1=高速、9=高圧縮）
        level: 6,
        // メモリ使用量を制限
        memLevel: 7
    },
    // 最大ペイロードサイズを制限（1MB）
    maxPayload: 1024 * 1024,
    // クライアント検証
    verifyClient: (_info) => {
        // 必要に応じてクライアント検証ロジックを追加
        return true;
    }
});

// ユーザー・クライアント管理
class UserManager {
    constructor() {
        this.users = new Map(); // userId -> User
        this.clients = new Map(); // clientId -> Client
        this.userSessions = new Map(); // userId -> Set of clientIds
        this.chatHistory = []; // チャット履歴を保存
        this.maxHistorySize = 50; // 最大履歴数
        this.cleanupInterval = null;
        this.startCleanupTimer();
    }

    // ユーザー作成・取得
    getOrCreateUser(userId, username) {
        if (!this.users.has(userId)) {
            this.users.set(userId, {
                id: userId,
                username: username,
                createdAt: Date.now(),
                lastActivity: Date.now(),
                isOnline: false
            });
            this.userSessions.set(userId, new Set());
        } else {
            // ユーザー名を更新
            this.users.get(userId).username = username;
            this.users.get(userId).lastActivity = Date.now();
        }
        return this.users.get(userId);
    }

    // ユーザー名変更
    changeUsername(clientId, newUsername) {
        const client = this.clients.get(clientId);
        if (!client) return;
        const oldUsername = client.username;
        client.username = newUsername;
        this.users.get(client.userId).username = newUsername;
        this.users.get(client.userId).lastActivity = Date.now();
        return oldUsername;
    }

    // クライアント追加
    addClient(clientId, ws, userId, username) {
        const user = this.getOrCreateUser(userId, username);

        const client = {
            id: clientId,
            ws: ws,
            userId: userId,
            username: username,
            connectedAt: Date.now(),
            lastPing: Date.now(),
            isAlive: true
        };

        this.clients.set(clientId, client);
        this.userSessions.get(userId).add(clientId);
        user.isOnline = true;
        user.lastActivity = Date.now();

        console.log(`User ${username} (${userId}) connected with client ${clientId}`);
        return client;
    }

    // クライアント削除
    removeClient(clientId) {
        const client = this.clients.get(clientId);
        if (!client) return;

        const userId = client.userId;
        this.clients.delete(clientId);

        if (this.userSessions.has(userId)) {
            this.userSessions.get(userId).delete(clientId);

            // ユーザーの全セッションが切断された場合
            if (this.userSessions.get(userId).size === 0) {
                const user = this.users.get(userId);
                if (user) {
                    user.isOnline = false;
                    user.lastActivity = Date.now();
                }
            }
        }

        console.log(`Client ${clientId} (user: ${client.username}) disconnected`);
    }

    // アクティブユーザー一覧取得
    getActiveUsers() {
        return Array.from(this.users.values())
            .filter(user => user.isOnline)
            .map(user => ({
                id: user.id,
                username: user.username,
                connectedClients: this.userSessions.get(user.id)?.size || 0
            }));
    }

    // クライアント取得
    getClient(clientId) {
        return this.clients.get(clientId);
    }

    // ユーザーの全クライアント取得
    getUserClients(userId) {
        const clientIds = this.userSessions.get(userId) || new Set();
        return Array.from(clientIds).map(id => this.clients.get(id)).filter(Boolean);
    }

    // 全クライアント取得
    getAllClients() {
        return Array.from(this.clients.values());
    }

    // Ping更新
    updatePing(clientId) {
        const client = this.clients.get(clientId);
        if (client) {
            client.lastPing = Date.now();
            client.isAlive = true;
        }
    }

    // デッドコネクション検出・クリーンアップ
    startCleanupTimer() {
        this.cleanupInterval = setInterval(() => {
            const now = Date.now();
            const deadClients = [];

            // 60秒以上Pingがないクライアントを検出
            for (const [clientId, client] of this.clients) {
                if (now - client.lastPing > 60000) {
                    deadClients.push(clientId);
                }
            }

            // デッドクライアントを削除
            deadClients.forEach(clientId => {
                const client = this.clients.get(clientId);
                if (client && client.ws.readyState !== ws.CLOSED) {
                    console.log(`Terminating dead connection: ${clientId}`);
                    client.ws.terminate();
                }
                this.removeClient(clientId);
            });

            // 非アクティブユーザーのクリーンアップ（24時間後）
            for (const [userId, user] of this.users) {
                if (!user.isOnline && now - user.lastActivity > 24 * 60 * 60 * 1000) {
                    this.users.delete(userId);
                    this.userSessions.delete(userId);
                    console.log(`Cleaned up inactive user: ${user.username} (${userId})`);
                }
            }
        }, 30000); // 30秒ごとにクリーンアップ
    }

    // リソースクリーンアップ
    cleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }

        // 全クライアント切断
        for (const client of this.clients.values()) {
            if (client.ws.readyState !== ws.CLOSED) {
                client.ws.terminate();
            }
        }

        this.clients.clear();
        this.users.clear();
        this.userSessions.clear();
    }

    // チャット履歴に追加
    addChatMessage(message, fromUser, timestamp) {
        const chatMessage = {
            message: message,
            fromUser: fromUser,
            timestamp: timestamp
        };

        this.chatHistory.push(chatMessage);

        // 履歴サイズ制限
        if (this.chatHistory.length > this.maxHistorySize) {
            this.chatHistory.shift(); // 古いメッセージを削除
        }
    }

    // チャット履歴を取得
    getChatHistory() {
        return this.chatHistory;
    }
}

const userManager = new UserManager();

// MIDIイベントの処理（複数バッファ対応）
function processMidiEvents(eventBuffers, senderId) {
    try {
        const senderClient = userManager.getClient(senderId);
        if (!senderClient) return;

        // バッファが配列の配列でない場合は配列に変換（後方互換性）
        const buffers = Array.isArray(eventBuffers[0]) ? eventBuffers : [eventBuffers];

        // メッセージを事前に作成（JSON.stringifyを一度だけ実行）
        const message = JSON.stringify({
            type: 'midi',
            buffers: buffers,
            fromUser: {
                id: senderClient.userId,
                username: senderClient.username
            },
            timestamp: Date.now()
        });

        // 送信者以外の全クライアントにブロードキャスト
        const allClients = userManager.getAllClients();
        const targetClients = [];

        // 送信対象クライアントを事前にフィルタリング
        for (const client of allClients) {
            if (client.id !== senderId && client.ws.readyState === ws.OPEN) {
                targetClients.push(client);
            }
        }

        // 全クライアントに非同期でブロードキャスト
        sendToBatch(targetClients, message);
    } catch (error) {
        console.error('Error processing MIDI events:', error);
    }
}

// チャットメッセージの処理
function processChatMessage(message, senderId) {
    try {
        const senderClient = userManager.getClient(senderId);
        if (!senderClient) return;

        // メッセージの基本検証
        if (!message || typeof message !== 'string' || message.trim().length === 0) {
            return;
        }

        // メッセージ長制限（1000文字）
        const trimmedMessage = message.trim().substring(0, 1000);

        // チャットメッセージを作成
        const chatMessage = JSON.stringify({
            type: 'chat',
            message: trimmedMessage,
            fromUser: {
                id: senderClient.userId,
                username: senderClient.username
            },
            timestamp: Date.now()
        });

        // 全クライアントにブロードキャスト（送信者含む）
        const allClients = userManager.getAllClients();
        const targetClients = [];

        // 送信対象クライアントをフィルタリング
        for (const client of allClients) {
            if (client.ws.readyState === ws.OPEN) {
                targetClients.push(client);
            }
        }

        // 全クライアントに非同期送信
        sendToBatch(targetClients, chatMessage);

        // チャット履歴に追加
        userManager.addChatMessage(trimmedMessage, {
            id: senderClient.userId,
            username: senderClient.username
        }, Date.now());

        console.log(`Chat from ${senderClient.username}: ${trimmedMessage}`);
    } catch (error) {
        console.error('Error processing chat message:', error);
    }
}

// バッチ送信関数（改善版：バックプレッシャー対応）
function sendToBatch(clients, message) {
    const promises = [];

    for (const client of clients) {
        // 各クライアントへの送信を非同期で処理
        const sendPromise = sendToClientSafely(client, message);
        promises.push(sendPromise);
    }

    // 全ての送信を並行実行（ただし結果は待たない）
    Promise.allSettled(promises).then(results => {
        const failures = results.filter(result => result.status === 'rejected');
        if (failures.length > 0) {
            console.log(`Broadcast completed with ${failures.length} failures out of ${clients.length} clients`);
        }
    }).catch(error => {
        console.error('Error in batch send promise handling:', error);
    });
}

// 安全なクライアント送信関数
async function sendToClientSafely(client, message) {
    return new Promise((resolve, reject) => {
        try {
            // WebSocketの状態をチェック
            if (client.ws.readyState !== ws.OPEN) {
                reject(new Error(`WebSocket not open: ${client.ws.readyState}`));
                return;
            }

            // バッファサイズをチェック（WebSocketライブラリの内部バッファ）
            const bufferedAmount = client.ws._sender ? client.ws._sender._bufferedBytes : 0;
            const maxBufferSize = 1024 * 1024; // 1MB制限

            if (bufferedAmount > maxBufferSize) {
                console.warn(`Client ${client.id} buffer overflow (${bufferedAmount} bytes), skipping send`);
                reject(new Error(`Buffer overflow: ${bufferedAmount} bytes`));
                return;
            }

            // コールバック付きで送信
            client.ws.send(message, (error) => {
                if (error) {
                    console.error(`Failed to send to client ${client.id}:`, error);
                    // 送信失敗したクライアントを削除
                    userManager.removeClient(client.id);
                    reject(error);
                } else {
                    resolve();
                }
            });

        } catch (error) {
            console.error(`Error sending to client ${client.id}:`, error);
            userManager.removeClient(client.id);
            reject(error);
        }
    });
}

// MIDIバッファの解析（デバッグ用、現在未使用）
function parseMidiBuffer(buffer) {
    const events = [];

    // バッファが配列として送られてくることを想定
    if (Array.isArray(buffer)) {
        for (let i = 0; i < buffer.length; i += 8) { // 8バイトずつ処理
            if (i + 7 < buffer.length) {
                const deltaTime = (buffer[i + 6] << 8) | buffer[i + 7];
                const event = {
                    type: buffer[i] === 144 ? 'noteon' : buffer[i] === 128 ? 'noteoff' : 'unknown',
                    notenum: buffer[i + 1],
                    velocity: buffer[i + 2],
                    color: `#${buffer[i + 3].toString(16).padStart(2, '0')}${buffer[i + 4].toString(16).padStart(2, '0')}${buffer[i + 5].toString(16).padStart(2, '0')}`,
                    deltaTime: deltaTime
                };担っちゃう問題を直して
                events.push(event);
            }
        }
    }

    return events;
}

// Pingイベントの定期送信とヘルスチェック
let pingInterval = null;

function startPingInterval() {
    if (pingInterval) {
        clearInterval(pingInterval);
    }

    pingInterval = setInterval(() => {
        const allClients = userManager.getAllClients();

        allClients.forEach((client) => {
            if (client.ws.readyState === ws.OPEN) {
                try {
                    client.isAlive = false; // Pongで再びtrueになる
                    client.ws.ping();
                } catch (error) {
                    console.error(`Failed to ping client ${client.id}:`, error);
                    userManager.removeClient(client.id);
                }
            } else {
                // 切断されたクライアントを削除
                userManager.removeClient(client.id);
            }
        });
    }, 30000); // 30秒ごとにping
}

// サーバー終了時のクリーンアップ
function cleanup() {
    console.log('Cleaning up server resources...');

    if (pingInterval) {
        clearInterval(pingInterval);
        pingInterval = null;
    }

    userManager.cleanup();

    if (wss) {
        wss.close(() => {
            console.log('WebSocket server closed');
        });
    }

    if (server) {
        server.close(() => {
            console.log('HTTP server closed');
        });
    }
}

// プロセス終了時のクリーンアップ
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    cleanup();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    cleanup();
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    cleanup();
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    cleanup();
    process.exit(1);
});

wss.on('connection', (ws) => {
    // クライアントにユニークIDを割り当て
    const clientId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
    ws.id = clientId;

    // WebSocketのバッファリング設定を最適化
    if (ws._socket) {
        // TCP_NODELAYを有効にして小さなパケットの遅延を減らす
        ws._socket.setNoDelay(true);
        // Keep-Aliveを有効にして接続の安定性を向上
        ws._socket.setKeepAlive(true, 30000);
    }

    // ランダムなユーザーIDとユーザー名を生成
    const userId = 'user_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
    const username = 'Anonymeows' + Math.floor(Math.random() * 1000);

    // 自動的にユーザーを作成・登録
    const client = userManager.addClient(clientId, ws, userId, username);

    console.log(`New connection: ${clientId} (${username})`);

    // 接続確認メッセージを送信
    ws.send(JSON.stringify({
        type: 'connected',
        clientId: clientId,
        user: {
            id: userId,
            username: username
        },
        activeUsers: userManager.getActiveUsers(),
        timestamp: Date.now()
    }));

    // チャット履歴を送信
    const chatHistory = userManager.getChatHistory();
    if (chatHistory.length > 0) {
        ws.send(JSON.stringify({
            type: 'chat_history',
            messages: chatHistory,
            timestamp: Date.now()
        }));
    }

    // 他のユーザーに新規ユーザー通知
    broadcastUserUpdate();

    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);

            switch (data.type) {
                case 'update_username':
                    // ユーザー名更新
                    if (data.username && client) {
                        const oldUsername = client.username;
                        client.username = data.username;
                        userManager.getOrCreateUser(client.userId, data.username);

                        console.log(`User ${oldUsername} changed name to ${data.username}`);

                        // 更新成功レスポンス
                        ws.send(JSON.stringify({
                            type: 'username_updated',
                            username: data.username,
                            timestamp: Date.now()
                        }));

                        // 他のユーザーに更新通知
                        broadcastUserUpdate();
                    }
                    break;

                case 'midi':
                    processMidiEvents(data.buffers, clientId);
                    // 受信確認を送信
                    ws.send(JSON.stringify({
                        type: 'midi_ack',
                        timestamp: Date.now()
                    }));
                    break;

                case 'ping':
                    // Pongを返す
                    userManager.updatePing(clientId);
                    ws.send(JSON.stringify({
                        type: 'pong',
                        timestamp: Date.now()
                    }));
                    break;

                case 'get_users':
                    ws.send(JSON.stringify({
                        type: 'user_list',
                        users: userManager.getActiveUsers(),
                        timestamp: Date.now()
                    }));
                    break;

                case 'set_username':
                    if (data.username && client) {
                        const oldUsername = userManager.changeUsername(clientId, data.username);
                        console.log(`User ${oldUsername} changed name to ${data.username}`);
                        broadcastUserUpdate();
                    }
                    break;

                case 'chat':
                    // チャットメッセージの処理
                    if (data.message && client) {
                        processChatMessage(data.message, clientId);
                    }
                    break;

                default:
                    console.log(`Unknown message type: ${data.type}`);
            }
        } catch (error) {
            console.error(`Error processing message from ${clientId}:`, error);
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid message format',
                timestamp: Date.now()
            }));
        }
    });

    ws.on('pong', () => {
        userManager.updatePing(clientId);
        if (client) {
            client.isAlive = true;
        }
    });

    ws.on('close', () => {
        userManager.removeClient(clientId);
        broadcastUserUpdate();
        console.log(`Connection closed: ${clientId}`);
    });

    ws.on('error', (error) => {
        console.error(`WebSocket error for ${clientId}:`, error);
        userManager.removeClient(clientId);
        broadcastUserUpdate();
    });
});

// ユーザー更新のブロードキャスト（改善版）
function broadcastUserUpdate() {
    const activeUsers = userManager.getActiveUsers();
    const allClients = userManager.getAllClients();

    // メッセージを事前に作成
    const message = JSON.stringify({
        type: 'user_update',
        users: activeUsers,
        timestamp: Date.now()
    });

    // アクティブなクライアントをフィルタリング
    const targetClients = allClients.filter(client =>
        client.ws.readyState === ws.OPEN
    );

    // バッチ送信を使用
    sendToBatch(targetClients, message);
}

// バッファ監視機能
function startBufferMonitoring() {
    setInterval(() => {
        const allClients = userManager.getAllClients();
        let totalBufferedBytes = 0;
        let clientsWithHighBuffer = 0;

        allClients.forEach(client => {
            if (client.ws._sender) {
                const bufferedBytes = client.ws._sender._bufferedBytes || 0;
                totalBufferedBytes += bufferedBytes;

                if (bufferedBytes > 512 * 1024) { // 512KB以上
                    clientsWithHighBuffer++;
                    console.warn(`Client ${client.id} has high buffer: ${Math.round(bufferedBytes / 1024)}KB`);
                }
            }
        });

        if (clientsWithHighBuffer > 0) {
            console.log(`Buffer monitoring: ${clientsWithHighBuffer}/${allClients.length} clients with high buffer, total: ${Math.round(totalBufferedBytes / 1024)}KB`);
        }
    }, 10000); // 10秒ごとに監視
}

// Pingの開始
startPingInterval();

// バッファ監視の開始
startBufferMonitoring();

server.listen(9191, () => {
    console.log('Server started on port 9191');
    console.log('MIDI WebSocket server ready for connections');
});