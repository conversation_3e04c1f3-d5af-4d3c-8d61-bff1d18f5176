# デルタタイム同期機能ガイド

## 概要

このガイドでは、MIDIイベントのデルタタイム同期機能について説明します。デルタタイムを使用することで、複数のMIDIイベントを正確なタイミングで再生し、クライアント間での同期を実現できます。

## デルタタイムとは

デルタタイムは、前のイベントからの相対的な時間差（ミリ秒）を表します。これにより、複数のMIDIイベントを時間的に正確に配置できます。

### 特徴
- **相対時間**: 前のイベントからの時間差
- **16ビット値**: 0-65535ミリ秒の範囲
- **同期制御**: クライアント間での正確なタイミング制御

## バッファ構造

### 8バイト形式
```
[0] ステータスバイト (144=NoteOn, 128=NoteOff)
[1] ノート番号 (0-127)
[2] ベロシティ (0-127)
[3] 色情報R (0-255)
[4] 色情報G (0-255)
[5] 色情報B (0-255)
[6] デルタタイム上位バイト (0-255)
[7] デルタタイム下位バイト (0-255)
```

### デルタタイムの計算
```javascript
// 16ビット値の作成
const deltaTime = 1000; // 1秒
const deltaTimeHigh = (deltaTime >> 8) & 0xFF;
const deltaTimeLow = deltaTime & 0xFF;

// 16ビット値の復元
const deltaTime = (deltaTimeHigh << 8) | deltaTimeLow;
```

## 使用例

### 1. 単一イベント送信
```javascript
// 即座に再生（デルタタイム = 0）
client.sendNoteOn(60, 80, 0xFF0000, 0);

// 500ms後に再生
client.sendNoteOn(64, 80, 0x00FF00, 500);
```

### 2. 複数イベント送信
```javascript
const buffers = [
    [144, 60, 80, 255, 0, 0, 0, 0],      // 即座にC4 Note On
    [144, 64, 80, 0, 255, 0, 1, 244],   // 500ms後にE4 Note On
    [128, 60, 0, 255, 0, 0, 1, 244],    // さらに500ms後にC4 Note Off
    [128, 64, 0, 0, 255, 0, 1, 244]     // さらに500ms後にE4 Note Off
];

ws.send(JSON.stringify({
    type: 'midi',
    buffers: buffers
}));
```

### 3. 和音の演奏
```javascript
// 同時に3つの音を鳴らす
const chord = [
    [144, 60, 80, 255, 0, 0, 0, 0],    // C4 - 即座
    [144, 64, 80, 255, 0, 0, 0, 0],    // E4 - 即座
    [144, 67, 80, 255, 0, 0, 0, 0]     // G4 - 即座
];
```

### 4. アルペジオの演奏
```javascript
// 順次演奏（100msずつ遅延）
const arpeggio = [
    [144, 60, 80, 255, 0, 0, 0, 0],     // C4 - 即座
    [144, 64, 80, 255, 0, 0, 0, 100],   // E4 - 100ms後
    [144, 67, 80, 255, 0, 0, 0, 100],   // G4 - さらに100ms後
    [144, 72, 80, 255, 0, 0, 0, 100]    // C5 - さらに100ms後
];
```

## クライアント実装

### JavaScript/HTML
```javascript
// デルタタイム付きでイベント送信
sendMidiEvent(type, notenum, velocity, color, deltaTime = 0) {
    const buffer = [
        type === 'noteon' ? 144 : 128,
        notenum,
        velocity,
        parseInt(color.substr(1, 2), 16),
        parseInt(color.substr(3, 2), 16),
        parseInt(color.substr(5, 2), 16),
        (deltaTime >> 8) & 0xFF,
        deltaTime & 0xFF
    ];
    
    this.ws.send(JSON.stringify({
        type: 'midi',
        buffers: [buffer]
    }));
}

// 受信時のスケジューリング
scheduleNoteVisualization(event) {
    setTimeout(() => {
        this.visualizeNote(event);
    }, event.deltaTime || 0);
}
```

### C++
```cpp
// デルタタイム付きバッファ作成
std::vector<uint8_t> createMidiBuffer(uint8_t statusByte, uint8_t notenum, 
                                     uint8_t velocity, uint32_t color, uint16_t deltaTime = 0) {
    return {
        statusByte,
        notenum,
        velocity,
        static_cast<uint8_t>((color >> 16) & 0xFF),
        static_cast<uint8_t>((color >> 8) & 0xFF),
        static_cast<uint8_t>(color & 0xFF),
        static_cast<uint8_t>((deltaTime >> 8) & 0xFF),
        static_cast<uint8_t>(deltaTime & 0xFF)
    };
}

// Note On送信
void sendNoteOn(uint8_t notenum, uint8_t velocity, uint32_t color, uint16_t deltaTime = 0);
```

### Python
```python
def _create_midi_buffer(self, status_byte: int, notenum: int, velocity: int, 
                       color: int, delta_time: int = 0) -> List[int]:
    return [
        status_byte,
        notenum,
        velocity,
        (color >> 16) & 0xFF,
        (color >> 8) & 0xFF,
        color & 0xFF,
        (delta_time >> 8) & 0xFF,
        delta_time & 0xFF
    ]

def send_note_on(self, notenum: int, velocity: int, color: int, delta_time: int = 0):
    buffer = self._create_midi_buffer(144, notenum, velocity, color, delta_time)
    self._send_midi_buffer(buffer)
```

## 同期のベストプラクティス

### 1. タイミング精度
- デルタタイムは相対時間なので、累積誤差を避けるため適切に設計
- 長時間の遅延（>65秒）は複数のイベントに分割

### 2. ネットワーク遅延の考慮
- クライアント間のネットワーク遅延を考慮した設計
- 重要なタイミングでは余裕を持ったデルタタイム設定

### 3. パフォーマンス
- 大量のイベントを一度に送信する場合は適切にバッチ処理
- 受信側でのスケジューリング処理の最適化

## 注意事項

1. **デルタタイムの制限**: 最大65535ms（約65秒）
2. **累積遅延**: 複数のイベントチェーンでは累積遅延に注意
3. **ブラウザ制限**: setTimeout の精度はブラウザに依存
4. **同期精度**: ネットワーク遅延により完全な同期は困難

## トラブルシューティング

### よくある問題
1. **タイミングのずれ**: ネットワーク遅延やクライアント処理能力の差
2. **イベントの欠落**: バッファサイズやネットワークエラー
3. **累積誤差**: 長時間のイベントチェーンでの時間ずれ

### 解決方法
1. **適切なバッファリング**: 受信側でのイベントキューイング
2. **エラーハンドリング**: 欠落イベントの検出と補完
3. **時間補正**: 定期的な時間同期メカニズム
